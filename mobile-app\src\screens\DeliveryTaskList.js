import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    FlatList,
    TouchableOpacity,
    StyleSheet,
    RefreshControl,
    Alert,
    ActivityIndicator
} from 'react-native';
import { Header, SearchBar } from 'react-native-elements';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { colors } from '../common/theme';

const DeliveryTaskList = ({ navigation }) => {
    const { t } = useTranslation();

    // Local state
    const [filteredCustomers, setFilteredCustomers] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [refreshing, setRefreshing] = useState(false);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        loadDeliveryData();
    }, [bookingData]);

    useEffect(() => {
        filterCustomers();
    }, [searchQuery, deliveryTasksData.customers]);

    useEffect(() => {
        // Actualizar customers cuando cambien los datos de Redux
        if (deliveryTasksData.customers) {
            setCustomers(deliveryTasksData.customers);
        }
    }, [deliveryTasksData.customers]);

    const loadDeliveryData = () => {
        try {
            setLoading(true);

            // Por ahora, usar datos mock para evitar crashes
            const mockCustomers = [
                {
                    id: '1',
                    name: 'Juan Pérez',
                    phone: '+52 ************',
                    address: 'Av. Revolución 123, Col. Centro, Guadalajara',
                    totalProducts: 3,
                    deliveredProducts: 1,
                    progress: 33.33,
                    orders: []
                },
                {
                    id: '2',
                    name: 'María González',
                    phone: '+52 ************',
                    address: 'Calle Morelos 456, Col. Americana, Guadalajara',
                    totalProducts: 2,
                    deliveredProducts: 0,
                    progress: 0,
                    orders: []
                }
            ];

            setTimeout(() => {
                setFilteredCustomers(mockCustomers);
                setLoading(false);
            }, 1000);

        } catch (error) {
            console.error('Error loading delivery data:', error);
            setLoading(false);
            Alert.alert(t('error') || 'Error', error.message || 'Error loading data');
        }
    };

    const filterCustomers = () => {
        const customersToFilter = deliveryTasksData.customers || [];
        if (!searchQuery.trim()) {
            setFilteredCustomers(customersToFilter);
        } else {
            const filtered = customersToFilter.filter(customer =>
                customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                customer.phone.includes(searchQuery) ||
                customer.address.toLowerCase().includes(searchQuery.toLowerCase())
            );
            setFilteredCustomers(filtered);
        }
    };

    const handleRefresh = async () => {
        setRefreshing(true);
        // Aquí podrías recargar los datos desde el servidor
        await new Promise(resolve => setTimeout(resolve, 1000));
        loadDeliveryData();
        setRefreshing(false);
    };

    // Función para navegar al detalle del cliente
    const navigateToCustomerDetail = (customer) => {
        Alert.alert('Info', 'Funcionalidad en desarrollo - Detalle de cliente: ' + customer.name);
        // navigation.navigate('CustomerProductList', { customer });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'COMPLETED':
                return colors.GREEN;
            case 'PARTIAL':
                return colors.YELLOW;
            case 'PENDING':
            default:
                return colors.GREY;
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'COMPLETED':
                return 'checkmark-circle';
            case 'PARTIAL':
                return 'checkmark-circle-outline';
            case 'PENDING':
            default:
                return 'ellipse-outline';
        }
    };

    const renderCustomerItem = ({ item }) => (
        <TouchableOpacity
            style={styles.customerCard}
            onPress={() => navigateToCustomerDetail(item)}
        >
            <View style={styles.customerHeader}>
                <View style={styles.customerInfo}>
                    <Text style={styles.customerName}>{item.name}</Text>
                    <Text style={styles.customerAddress} numberOfLines={2}>
                        {item.address}
                    </Text>
                </View>
                <View style={styles.statusContainer}>
                    <Ionicons
                        name={getStatusIcon(item.progress === 100 ? 'COMPLETED' : item.progress > 0 ? 'PARTIAL' : 'PENDING')}
                        size={24}
                        color={getStatusColor(item.progress === 100 ? 'COMPLETED' : item.progress > 0 ? 'PARTIAL' : 'PENDING')}
                    />
                </View>
            </View>
            
            <View style={styles.progressContainer}>
                <View style={styles.progressInfo}>
                    <Text style={styles.progressText}>
                        {item.deliveredProducts}/{item.totalProducts} {t('products_delivered')}
                    </Text>
                    <Text style={styles.statusText}>
                        {item.progress === 100 ? t('completed') :
                         item.progress > 0 ? t('in_progress') : t('pending')}
                    </Text>
                </View>
                <View style={styles.progressBar}>
                    <View
                        style={[
                            styles.progressFill,
                            {
                                width: `${item.progress}%`,
                                backgroundColor: getStatusColor(item.progress === 100 ? 'COMPLETED' : item.progress > 0 ? 'PARTIAL' : 'PENDING')
                            }
                        ]}
                    />
                </View>
            </View>

            <View style={styles.customerActions}>
                <Ionicons name="chevron-forward" size={20} color={colors.GREY} />
            </View>
        </TouchableOpacity>
    );

    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.BLUE} />
                <Text style={styles.loadingText}>{t('loading_delivery_data')}</Text>
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <Header
                centerComponent={{
                    text: t('delivery_task_list'),
                    style: { color: colors.WHITE, fontSize: 18, fontWeight: 'bold' }
                }}
                leftComponent={{
                    icon: 'arrow-back',
                    color: colors.WHITE,
                    onPress: () => navigation.goBack()
                }}
                backgroundColor={colors.BLUE}
            />

            <SearchBar
                placeholder={t('search_customers')}
                onChangeText={setSearchQuery}
                value={searchQuery}
                containerStyle={styles.searchContainer}
                inputContainerStyle={styles.searchInputContainer}
                inputStyle={styles.searchInput}
                searchIcon={{ color: colors.GREY }}
                clearIcon={{ color: colors.GREY }}
            />

            <View style={styles.summaryContainer}>
                <Text style={styles.summaryText}>
                    {filteredCustomers.length} {t('customers')} • {' '}
                    {filteredCustomers.filter(c => c.status === 'COMPLETED').length} {t('completed')}
                </Text>
            </View>

            <FlatList
                data={filteredCustomers}
                renderItem={renderCustomerItem}
                keyExtractor={(item) => item.id}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={[colors.BLUE]}
                    />
                }
                contentContainerStyle={styles.listContainer}
                showsVerticalScrollIndicator={false}
            />


        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.BACKGROUND_PRIMARY,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.WHITE,
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
        color: colors.GREY,
    },
    searchContainer: {
        backgroundColor: colors.WHITE,
        borderBottomColor: colors.GREY,
        borderTopColor: colors.GREY,
        paddingHorizontal: 16,
        paddingVertical: 8,
    },
    searchInputContainer: {
        backgroundColor: colors.BACKGROUND_PRIMARY,
        borderRadius: 8,
    },
    searchInput: {
        fontSize: 16,
        color: colors.BLACK,
    },
    summaryContainer: {
        backgroundColor: colors.WHITE,
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: colors.GREY,
    },
    summaryText: {
        fontSize: 14,
        color: colors.GREY,
        textAlign: 'center',
    },
    listContainer: {
        paddingVertical: 8,
    },
    customerCard: {
        backgroundColor: colors.WHITE,
        marginHorizontal: 16,
        marginVertical: 4,
        borderRadius: 8,
        padding: 16,
        elevation: 2,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
    },
    customerHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    customerInfo: {
        flex: 1,
        marginRight: 12,
    },
    customerName: {
        fontSize: 16,
        fontWeight: 'bold',
        color: colors.BLACK,
        marginBottom: 4,
    },
    customerAddress: {
        fontSize: 14,
        color: colors.GREY,
        lineHeight: 18,
    },
    statusContainer: {
        alignItems: 'center',
    },
    progressContainer: {
        marginBottom: 8,
    },
    progressInfo: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    progressText: {
        fontSize: 14,
        color: colors.BLACK,
        fontWeight: '500',
    },
    statusText: {
        fontSize: 12,
        color: colors.GREY,
        textTransform: 'uppercase',
    },
    progressBar: {
        height: 4,
        backgroundColor: colors.GREY,
        borderRadius: 2,
        overflow: 'hidden',
    },
    progressFill: {
        height: '100%',
        borderRadius: 2,
    },
    customerActions: {
        alignItems: 'flex-end',
    },
});

export default DeliveryTaskList;
